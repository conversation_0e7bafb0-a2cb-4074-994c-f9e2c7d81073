import { useState } from "react";
import axios from "axios";

export const useRegister = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleRegister = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await axios.post(
        "http://localhost:3000/api/auth/register",
        formData
      );

      if (response.data?.success) {
        setSuccess(true);
      } else {
        setError(response.data?.message || "Registration failed");
      }
    } catch (err) {
      setError(err.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return { handleRegister, loading, error, success };
};
