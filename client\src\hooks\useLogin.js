import { useState } from "react";
import axios from "axios";

export const useLogin = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleLogin = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await axios.post(
        "http://localhost:3000/api/auth/login",
        formData
      );

      if (response.data?.success) {
        setSuccess(true);
        // Store token if provided
        if (response.data.token) {
          localStorage.setItem("token", response.data.token);
        }
        return response.data;
      } else {
        setError(response.data?.message || "Login failed");
        throw new Error(response.data?.message || "Login failed");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Something went wrong";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { handleLogin, loading, error, success };
};
