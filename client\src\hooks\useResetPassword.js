import { useState } from "react";
import axios from "axios";

export const useResetPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  const handleResetPassword = async (formData, token) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      const response = await axios.post(
        `http://localhost:3000/api/auth/reset-password/${token}`,
        formData
      );

      if (response.data?.success) {
        setSuccess(true);
        return response.data;
      } else {
        setError(response.data?.message || "Failed to reset password");
        throw new Error(response.data?.message || "Failed to reset password");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Something went wrong";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return { handleResetPassword, loading, error, success };
};
