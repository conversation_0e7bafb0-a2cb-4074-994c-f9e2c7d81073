const mongoose = require("mongoose");

const languageSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    level: {
      type: String,
      enum: ["basic", "conversational", "fluent", "native"],
      required: true,
    },
  },
  { _id: false }
);

const socialLinksSchema = new mongoose.Schema(
  {
    website: String,
    linkedin: String,
    github: String,
    twitter: String,
  },
  { _id: false }
);

const sellerProfileSchema = new mongoose.Schema(
  {
    title: { type: String, maxlength: 100 },
    description: { type: String, maxlength: 1000 },
    rating: { type: Number, default: 0 },
    reviewsCount: { type: Number, default: 0 },
    responseTime: Number, // avg response time in hours
    earnings: { type: Number, default: 0 },
  },
  { _id: false }
);

const userSchema = new mongoose.Schema(
  {
    // Core credentials
    username: {
      type: String,
      required: true,
      unique: true,
      minlength: 3,
      maxlength: 20,
      match: /^[a-zA-Z0-9_.]+$/,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      match: /^\S+@\S+\.\S+$/,
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
      select: false,
    },

    // User roles
    role: {
      type: String,
      enum: ["buyer", "seller", "admin"],
      default: "buyer",
    },
    isSeller: {
      type: Boolean,
      default: false,
    },

    // Profile
    fullName: String,
    bio: { type: String, maxlength: 500 },
    avatar: String, // URL to profile image
    location: String,
    languages: [languageSchema],
    skills: [String],
    socialLinks: socialLinksSchema,

    // Seller-specific
    sellerProfile: sellerProfileSchema,

    // Verification
    isVerified: { type: Boolean, default: false },
    emailVerifyToken: String,
    emailVerifyExpires: Date,

    // Reset password
    passwordResetToken: String,
    passwordResetExpires: Date,

    // Resend control
    emailResendCount: { type: Number, default: 0 },
    lastEmailResendAt: Date,
    passwordResetCount: { type: Number, default: 0 },
    lastPasswordResetAt: Date,

    // Other
    lastLogin: Date,
    twoFactorEnabled: { type: Boolean, default: false },
    isBanned: { type: Boolean, default: false },
  },
  {
    timestamps: true, // adds createdAt and updatedAt
  }
);

module.exports = mongoose.model("User", userSchema);
