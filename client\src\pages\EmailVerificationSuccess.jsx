import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Mail } from "lucide-react";

export default function EmailVerificationSuccess() {
  const navigate = useNavigate();
  const location = useLocation();

  // Get email from navigation state
  const email = location.state?.email;

  const handleGoToLogin = () => {
    navigate("/login");
  };

  return (
   <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-sm">
        <CardContent className="pt-8 pb-8">
          <div className="text-center space-y-8">
            {/* Success Icon */}
            <div className="flex justify-center">
              <div className="h-16 w-16 bg-green-50 dark:bg-green-950 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
            
            {/* Main Content */}
            <div className="space-y-4">
              <h1 className="text-2xl font-medium text-foreground">
                Check your email
              </h1>
              
              <div className="space-y-3">
                <p className="text-muted-foreground text-sm leading-relaxed">
                  We've sent a verification link to
                </p>
                
                {email ? (
                  <div className="bg-muted/50 rounded-lg px-4 py-3 border">
                    <p className="text-sm font-medium text-foreground">
                      {email}
                    </p>
                  </div>
                ) : (
                  <div className="bg-muted/50 rounded-lg px-4 py-3 border">
                    <p className="text-sm text-muted-foreground">
                      your registered email address
                    </p>
                  </div>
                )}
                
                <p className="text-muted-foreground text-sm leading-relaxed">
                  Click the link in the email to activate your account.
                </p>
              </div>
            </div>
            
            {/* Action Button */}
            <Button 
              onClick={handleGoToLogin}
              className="w-full"
              size="lg"
            >
              Continue to Login
            </Button>
            
            {/* Help Text */}
            <p className="text-xs text-muted-foreground">
              Didn't receive the email? Check your spam folder
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
