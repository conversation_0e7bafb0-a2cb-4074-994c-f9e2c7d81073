import React from "react";
import { <PERSON>rowser<PERSON><PERSON><PERSON>, Route, Routes } from "react-router-dom";
import AuthLayout from "./pages/AuthLayout";
import EmailVerificationSuccess from "./pages/EmailVerificationSuccess";

const App = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<AuthLayout />} />
        <Route path="/login" element={<AuthLayout />} />
        <Route path="/register" element={<AuthLayout />} />
        <Route path="/forgot-password" element={<AuthLayout />} />
        <Route path="/reset-password" element={<AuthLayout />} />
        <Route path="/email-verification-success" element={<EmailVerificationSuccess />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;
