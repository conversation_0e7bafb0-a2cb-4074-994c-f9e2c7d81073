import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import AuthLayout from "./pages/AuthLayout";

const App = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<AuthLayout />} />
        <Route path="/register" element={<AuthLayout />} />
        <Route path="/forgot-password" element={<AuthLayout />} />
        <Route path="/reset-password" element={<AuthLayout />} />
      </Routes>
    </BrowserRouter>
  );
};

export default App;
